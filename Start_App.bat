@echo off
setlocal enabledelayedexpansion
title TMDB CSV Processor - Starting...
color 0A

echo.
echo ========================================
echo   TMDB CSV Processor App - Starting
echo ========================================
echo.

REM Check if TMDB API key is configured
if not exist "backend\.env" (
    echo ERROR: Backend .env file not found!
    echo Please make sure the backend/.env file exists.
    pause
    exit /b 1
)

findstr /C:"your_tmdb_api_key_here" "backend\.env" >nul
if %errorlevel%==0 (
    echo WARNING: TMDB API key not configured!
    echo Please edit backend\.env and add your TMDB API key
    echo Get your API key from: https://www.themoviedb.org/settings/api
    echo.
    echo Press any key to continue anyway...
    pause >nul
)

echo.
echo Stopping any existing services...

REM Stop existing services on ports (simplified)
for /f "tokens=5" %%a in ('netstat -aon ^| findstr :8001 ^| findstr LISTENING') do (
    if not "%%a"=="0" (
        echo Stopping process %%a on port 8001...
        taskkill /PID %%a /F >nul 2>&1
    )
)

for /f "tokens=5" %%a in ('netstat -aon ^| findstr :3000 ^| findstr LISTENING') do (
    if not "%%a"=="0" (
        echo Stopping process %%a on port 3000...
        taskkill /PID %%a /F >nul 2>&1
    )
)

echo Waiting for ports to be released...
ping 127.0.0.1 -n 3 >nul

echo.
echo Starting backend service on port 8001...
start "TMDB Backend" cmd /c "cd /d "%~dp0" && py -m uvicorn backend.server:app --host 0.0.0.0 --port 8001 && pause"

echo Waiting for backend to start...
ping 127.0.0.1 -n 6 >nul

echo.
echo Starting frontend service on port 3000...
start "TMDB Frontend" cmd /c "cd /d "%~dp0" && cd frontend && npm start"

echo Waiting for frontend to start...
ping 127.0.0.1 -n 10 >nul

echo.
echo Verifying services started...

REM Check if backend is running
echo Checking backend on port 8001...
netstat -an | findstr :8001 | findstr LISTENING >nul 2>&1
if !errorlevel!==0 (
    echo ✅ Backend is running on port 8001
    set backend_running=1
) else (
    echo ❌ Backend is NOT running on port 8001
    set backend_running=0
)

REM Check if frontend is running
echo Checking frontend on port 3000...
netstat -an | findstr :3000 | findstr LISTENING >nul 2>&1
if !errorlevel!==0 (
    echo ✅ Frontend is running on port 3000
    set frontend_running=1
) else (
    echo ❌ Frontend is NOT running on port 3000
    set frontend_running=0
)

echo.
if !backend_running!==1 if !frontend_running!==1 (
    echo ========================================
    echo   TMDB CSV Processor App Started!
    echo ========================================
    echo.
    echo Frontend URL: http://localhost:3000
    echo Backend API:  http://localhost:8001
    echo API Docs:     http://localhost:8001/docs
    echo.
    echo Opening web browser...
    start "" "http://localhost:3000"

    echo.
    echo ✅ Application started successfully!
) else (
    echo ========================================
    echo   STARTUP ISSUES DETECTED!
    echo ========================================
    echo.
    if !backend_running!==0 (
        echo ❌ Backend failed to start on port 8001
        echo    Check if Python is installed and in PATH
        echo    Try running manually: python -m uvicorn backend.server:app --host 0.0.0.0 --port 8001
    )
    if !frontend_running!==0 (
        echo ❌ Frontend failed to start on port 3000
        echo    Check if Node.js is installed and dependencies are installed
        echo    Try running manually: cd frontend && npm start
    )
    echo.
    echo Please fix the issues above and try again.
)

echo.
echo To stop the application, run Stop_App.bat
echo.
echo Press any key to close this window...
echo (The services will continue running in background)
pause >nul
