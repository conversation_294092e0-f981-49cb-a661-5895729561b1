# TMDB App Startup - Bypass 0xc0000022 Error
# This script uses PowerShell to avoid the Python launch error

param(
    [switch]$Force
)

# No admin privileges required when using py.exe

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  TMDB App - Error 0xc0000022 Bypass" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Set working directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptDir

Write-Host "Working directory: $scriptDir" -ForegroundColor Green
Write-Host ""

# Test Python installations
$pythonPaths = @(
    "py.exe",
    "python.exe",
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe",
    "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe",
    "C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\python.exe"
)

$workingPython = $null

Write-Host "Testing Python installations..." -ForegroundColor Yellow
foreach ($pythonPath in $pythonPaths) {
    Write-Host "Testing: $pythonPath" -ForegroundColor Gray
    try {
        $result = & $pythonPath --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "SUCCESS: $pythonPath works" -ForegroundColor Green
            $workingPython = $pythonPath
            break
        } else {
            Write-Host "FAILED: $pythonPath doesn't work" -ForegroundColor Red
        }
    } catch {
        Write-Host "FAILED: $pythonPath - $($_.Exception.Message)" -ForegroundColor Red
    }
}

if (-not $workingPython) {
    Write-Host ""
    Write-Host "ERROR: No working Python installation found!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Solutions:" -ForegroundColor Yellow
    Write-Host "1. Install Python from Microsoft Store" -ForegroundColor White
    Write-Host "2. Reinstall Python from python.org" -ForegroundColor White
    Write-Host "3. Run: sfc /scannow" -ForegroundColor White
    Write-Host "4. Install Visual C++ Redistributables" -ForegroundColor White
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Using Python: $workingPython" -ForegroundColor Green
Write-Host ""

# Stop existing services
Write-Host "Stopping existing services..." -ForegroundColor Yellow
$processes3000 = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue | Select-Object -ExpandProperty OwningProcess
$processes8001 = Get-NetTCPConnection -LocalPort 8001 -ErrorAction SilentlyContinue | Select-Object -ExpandProperty OwningProcess

foreach ($pid in $processes3000) {
    try { Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue } catch {}
}
foreach ($pid in $processes8001) {
    try { Stop-Process -Id $pid -Force -ErrorAction SilentlyContinue } catch {}
}

Start-Sleep -Seconds 3

# Start backend using PowerShell process isolation (WORKING VERSION)
Write-Host "Starting backend service..." -ForegroundColor Green

$backendScript = @"
Set-Location '$scriptDir'
& '$workingPython' -m uvicorn backend.server:app --host 0.0.0.0 --port 8001
"@

$backendJob = Start-Job -ScriptBlock {
    param($script, $pythonPath)
    Invoke-Expression $script
} -ArgumentList $backendScript, $workingPython

Write-Host "Backend job started (ID: $($backendJob.Id))" -ForegroundColor Green

# Wait for backend to start
Write-Host "Waiting for backend to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 8

# Check if backend is running
$backendRunning = $false
try {
    $connection = Get-NetTCPConnection -LocalPort 8001 -State Listen -ErrorAction SilentlyContinue
    if ($connection) {
        Write-Host "SUCCESS: Backend is running on port 8001" -ForegroundColor Green
        $backendRunning = $true
    } else {
        Write-Host "ERROR: Backend failed to start" -ForegroundColor Red

        # Check job status
        $jobState = Get-Job -Id $backendJob.Id | Select-Object -ExpandProperty State
        Write-Host "Backend job state: $jobState" -ForegroundColor Yellow

        if ($jobState -eq "Failed") {
            $jobError = Receive-Job -Id $backendJob.Id 2>&1
            Write-Host "Backend error: $jobError" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "ERROR: Could not check backend status - $($_.Exception.Message)" -ForegroundColor Red
}

if (-not $backendRunning) {
    Write-Host ""
    Write-Host "Backend failed to start. Trying alternative method..." -ForegroundColor Yellow

    # Try direct process start
    try {
        $processInfo = New-Object System.Diagnostics.ProcessStartInfo
        $processInfo.FileName = $workingPython
        $processInfo.Arguments = "-m uvicorn backend.server:app --host 0.0.0.0 --port 8001"
        $processInfo.WorkingDirectory = $scriptDir
        $processInfo.UseShellExecute = $false
        $processInfo.CreateNoWindow = $false

        $process = [System.Diagnostics.Process]::Start($processInfo)
        Write-Host "Started backend process (PID: $($process.Id))" -ForegroundColor Green

        Start-Sleep -Seconds 5

        $connection = Get-NetTCPConnection -LocalPort 8001 -State Listen -ErrorAction SilentlyContinue
        if ($connection) {
            Write-Host "SUCCESS: Backend started with alternative method" -ForegroundColor Green
            $backendRunning = $true
        }
    } catch {
        Write-Host "Alternative method also failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}



# Start frontend
Write-Host ""
Write-Host "Starting frontend service..." -ForegroundColor Green

$frontendJob = Start-Job -ScriptBlock {
    param($workingDir)
    Set-Location "$workingDir\frontend"
    & npm start
} -ArgumentList $scriptDir

Start-Sleep -Seconds 10

# Check frontend
$frontendRunning = $false
try {
    $connection = Get-NetTCPConnection -LocalPort 3000 -State Listen -ErrorAction SilentlyContinue
    if ($connection) {
        Write-Host "SUCCESS: Frontend is running on port 3000" -ForegroundColor Green
        $frontendRunning = $true
    } else {
        Write-Host "ERROR: Frontend failed to start" -ForegroundColor Red
    }
} catch {
    Write-Host "ERROR: Could not check frontend status" -ForegroundColor Red
}

# Summary
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
if ($backendRunning -and $frontendRunning) {
    Write-Host "  APPLICATION STARTED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Frontend: http://localhost:3000" -ForegroundColor White
    Write-Host "Backend:  http://localhost:8001" -ForegroundColor White
    Write-Host ""

    Write-Host ""
    Write-Host "Opening browser..." -ForegroundColor Yellow
    Start-Process "http://localhost:3000"
    Write-Host ""
    Write-Host "SUCCESS: Bypassed Python 0xc0000022 error!" -ForegroundColor Green
} else {
    Write-Host "  STARTUP FAILED" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "The 0xc0000022 error persists. Try these solutions:" -ForegroundColor Yellow
    Write-Host "1. Disable Windows Defender Real-time Protection" -ForegroundColor White
    Write-Host "2. Run: sfc /scannow" -ForegroundColor White
    Write-Host "3. Install Visual C++ Redistributables" -ForegroundColor White
    Write-Host "4. Reinstall Python completely" -ForegroundColor White
}

Write-Host ""
Write-Host "Press Enter to continue (services will keep running)..." -ForegroundColor Cyan
Read-Host
